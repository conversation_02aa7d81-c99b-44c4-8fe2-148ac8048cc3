import { PrismaClient, SchemaBuilder } from "../types";

const exercise = (builder: SchemaBuilder, client: PrismaClient) => {
  builder.prismaObject("Exercise", {
    fields: (t) => ({
      id: t.exposeID("id"),
      name: t.exposeString("name"),
      createdAt: t.expose("createdAt", { type: "DateTime" }),
      updatedAt: t.expose("updatedAt", { type: "DateTime" }),
    }),
  });

  builder.queryType({
    fields: (t) => ({
      exercises: t.prismaField({
        errors: {},
        type: ["Exercise"],
        resolve: (query) => client.exercise.findMany({ ...query }),
      }),
    }),
  });
};

export { exercise };

import { UniqueError } from "../errors";
import { SchemaBuilder } from "../types";

const errors = (builder: SchemaBuilder) => {
  const ErrorInterface = builder.interfaceRef<Error>("Error").implement({
    fields: (t) => ({
      message: t.exposeString("message"),
    }),
  });

  builder.objectType(Error, {
    interfaces: [ErrorInterface],
    name: "GenericError",
  });

  builder.objectType(UniqueError, {
    interfaces: [ErrorInterface],
    name: "UniqueError",
    fields: (t) => ({
      attribute: t.exposeString("attribute"),
    }),
  });
};

export { errors };

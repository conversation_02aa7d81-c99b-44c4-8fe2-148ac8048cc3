import { UniqueError } from "../errors";
import { Prisma<PERSON>lient, SchemaBuilder } from "../types";

const userExercise = (builder: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, client: PrismaClient) => {
  builder.prismaObject("UserExercise", {
    fields: (t) => ({
      id: t.exposeID("id"),
      name: t.exposeString("name"),
      createdAt: t.expose("createdAt", { type: "DateTime" }),
      updatedAt: t.expose("updatedAt", { type: "DateTime" }),
    }),
  });

  builder.queryType({
    fields: (t) => ({
      userExercises: t.prismaField({
        errors: {},
        type: ["UserExercise"],
        resolve: (query, parent, args, context) =>
          client.userExercise.findMany({
            ...query,
            where: {
              userId: context.user.payload.id,
            },
          }),
      }),
    }),
  });

  builder.mutationType({
    fields: (t) => ({
      createUserExercise: t.prismaFieldWithInput({
        errors: { types: [UniqueError] },
        input: {
          name: t.input.string({ required: true }),
        },
        resolve: (query, parent, args, context) =>
          client.userExercise.create({
            ...query,
            data: {
              name: args.input.name,
              userId: context.user.payload.id,
            },
          }),
        type: "UserExercise",
      }),
    }),
  });
};

export { userExercise };

import SchemaBuilder from "@pothos/core";
import PothosPluginErrors from "@pothos/plugin-errors";
import PothosPluginPrisma from "@pothos/plugin-prisma";
import PothosPluginWithInput from "@pothos/plugin-with-input";
import { DateTimeResolver } from "graphql-scalars";

import type PrismaTypes from "./database/__generated__/graphql-schema";
import { getDatamodel } from "./database/__generated__/graphql-schema";
import { PrismaClient, User } from "./database/__generated__/orm/edge";
import PothosPluginRethrowPrismaErrors from "./plugins/rethrowPrismaErrors";
import { fieldNameToTypename } from "./utils";

const createBuilder = (
  client: PrismaClient,
  onUnusedQuery: "warn" | "error" | null,
) => {
  const builder = new SchemaBuilder<{
    Context: {
      user: {
        payload: User;
      };
    };
    PrismaTypes: PrismaTypes;
    Scalars: {
      DateTime: { Input: Date; Output: Date };
    };
  }>({
    errors: {
      defaultTypes: [Error],
      defaultResultOptions: {
        name: ({ fieldName }) => fieldNameToTypename(fieldName, "Data"),
      },
      defaultUnionOptions: {
        name: ({ fieldName }) => fieldNameToTypename(fieldName, "Result"),
      },
    },
    plugins: [
      PothosPluginRethrowPrismaErrors,
      PothosPluginErrors,
      PothosPluginPrisma,
      PothosPluginWithInput,
    ],
    prisma: {
      client,
      dmmf: getDatamodel(),
      onUnusedQuery,
    },
    withInput: {
      typeOptions: {
        name: ({ fieldName }) => fieldNameToTypename(fieldName, "Input"),
      },
    },
  });

  builder.addScalarType("DateTime", DateTimeResolver);

  return builder;
};

export { createBuilder };

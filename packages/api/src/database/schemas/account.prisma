model Account {
  id                    String    @id
  accessToken           String?
  accessTokenExpiresAt  DateTime?
  accountId             String
  idToken               String?
  password              String?
  providerId            String
  refreshToken          String?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId                String
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Account {
  id                    String    @id
  accessToken           String?
  accessTokenExpiresAt  DateTime?
  accountId             String
  idToken               String?
  password              String?
  providerId            String
  refreshToken          String?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId                String
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Exercise {
  id        Int      @id @default(autoincrement())
  name      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("exercise")
}

model JWKS {
  id         String   @id
  privateKey String
  publicKey  String
  createdAt  DateTime

  @@map("jwks")
}

datasource database {
  directUrl = env("DATABASE_DIRECT_URL")
  provider  = "postgresql"
  url       = env("DATABASE_URL") // Overridden by 'createClient' when executing within worker
}

generator graphql_schema {
  clientOutput      = "./orm"
  generateDatamodel = true
  output            = "../__generated__/graphql-schema.ts"
  provider          = "prisma-pothos-types"
}

generator orm {
  binaryTargets   = ["native", "debian-openssl-3.0.x"] // 'debian' to support execution on GitHub Actions
  output          = "../__generated__/orm"
  previewFeatures = ["driverAdapters"]
  provider        = "prisma-client-js"
}

model Session {
  id        String   @id
  expiresAt DateTime
  ipAddress String?
  token     String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userAgent String?
  userId    String
  createdAt DateTime
  updatedAt DateTime

  @@unique([token])
  @@map("session")
}

model User {
  id            String         @id
  accounts      Account[]
  email         String
  emailVerified Boolean
  image         String?
  name          String
  sessions      Session[]
  userExercises UserExercise[]
  createdAt     DateTime
  updatedAt     DateTime

  @@unique([email])
  @@map("user")
}

model UserExercise {
  id        Int      @id @default(autoincrement())
  name      String   @unique
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@unique([name, userId])
  @@map("user_exercise")
}

model Verification {
  id         String    @id
  expiresAt  DateTime
  identifier String
  value      String
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

import SchemaBuilder, { BasePlugin, type SchemaTypes } from "@pothos/core";

import { wrapResolve } from "./wrapResolve";

const pluginName = "rethrowPrismaErrors";

export class PothosPrismaErrorMapperPlugin<
  Types extends SchemaTypes,
> extends BasePlugin<Types> {
  override wrapResolve = wrapResolve;
}

SchemaBuilder.registerPlugin(pluginName, PothosPrismaErrorMapperPlugin);

export default pluginName;

import { createBuilder } from "./createBuilder";
import { createClient } from "./createClient";
import { createServer } from "./createServer";
import { createSchema } from "./graphql";

const fetch: ExportedHandlerFetchHandler<Env> = (request, env) => {
  const client = createClient(env.DATABASE_URL);
  const builder = createBuilder(
    client,
    env.DETECT_UNUSED_QUERY_ARGUMENTS ? "warn" : null,
  );
  const schema = createSchema(builder, client);
  const server = createServer(schema, env.AUTHENTICATION_URL);

  return server.fetch(request, env);
};

export { fetch };

import {
  createRemoteJwksSigningKeyProvider,
  useJWT,
} from "@graphql-yoga/plugin-jwt";

const jwt = (authenticationUrl: string) =>
  useJWT({
    extendContext: "user",
    signingKeyProviders: [
      createRemoteJwksSigningKeyProvider({
        jwksUri: `${authenticationUrl}/jwks`,
      }),
    ],
    tokenVerification: {
      algorithms: ["ES256"],
      audience: `${authenticationUrl}`,
      issuer: `${authenticationUrl}`,
    },
  });

export { jwt };

const renderGraphiQL = () => `
<!DOCTYPE html>
<html lang="en">
  <body style="margin: 0; overflow-x: hidden; overflow-y: hidden">
    <div id="sandbox" style="position:absolute;top:0;right:0;bottom:0;left:0"></div>
    <script src="https://embeddable-sandbox.cdn.apollographql.com/_latest/embeddable-sandbox.umd.production.min.js"></script>
    <script>
      new window.EmbeddedSandbox({
        initialEndpoint: window.location.href,
        target: "#sandbox",
      });
    </script>
  </body>
</html>`;

export { renderGraphiQL };

{"$schema": "node_modules/wrangler/config-schema.json", "compatibility_date": "2025-08-15", "compatibility_flags": ["nodejs_compat"], "dev": {"port": 9000}, "env": {"local": {}, "development": {"name": "rackup-api-dev", "preview_urls": false, "routes": [{"pattern": "api.dev.rackup.fitness", "custom_domain": true}], "workers_dev": false}}, "main": "src/index.ts", "name": "rackup-api", "observability": {"enabled": true}, "send_metrics": false}
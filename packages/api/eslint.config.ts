import eslintJs from "@eslint/js";
import eslintConfigPrettier from "eslint-config-prettier/flat";
import globals from "globals";
import typescriptEslint from "typescript-eslint";

const config = typescriptEslint.config(
  {
    extends: [
      eslintJs.configs.recommended,
      typescriptEslint.configs.recommended,
    ],
    files: ["src/**/*.ts"],
    languageOptions: { globals: globals.node },
  },
  {
    ignores: [
      "src/database/__generated__/**",
      "src/types/cloudflare-worker.d.ts",
    ],
  },
  /**
   * Prettier must be last
   * @see https://github.com/prettier/eslint-config-prettier#installation
   */
  eslintConfigPrettier,
);

export default config;

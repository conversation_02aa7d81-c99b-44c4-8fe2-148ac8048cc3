{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "checkJs": false, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["es2021"], "module": "es2022", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "es2021", "types": ["./src/types/cloudflare-worker.d.ts"]}, "include": ["src/types/cloudflare-worker.d.ts"]}
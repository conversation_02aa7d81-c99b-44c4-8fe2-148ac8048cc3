import { betterAuth } from "better-auth";
import { jwt } from "better-auth/plugins";
import { Pool } from "pg";

const createServer = (connectionString: string) =>
  betterAuth({
    advanced: {
      cookiePrefix: "rackup",
    },
    basePath: "/",
    database: new Pool({ connectionString }),
    emailAndPassword: {
      enabled: true,
    },
    plugins: [
      jwt({
        jwt: { expirationTime: "1h" },
        jwks: { keyPairConfig: { alg: "ES256" } },
      }),
    ],
  });

export { createServer };

import { Server } from "../../types";

const admin = async (server: Server) => {
  try {
    await server.api.signUpEmail({
      body: {
        email: "<EMAIL>",
        name: "Admin",
        password: process.env.ADMIN_PASSWORD!,
      },
    });

    console.log("🌱  The seed command has been executed.");
  } catch (error) {
    if (error instanceof Error) {
      const isUserAlreadyExists = error.message.includes("User already exists");
      if (isUserAlreadyExists) {
        console.log("Admin already exists. Skipping...");
        return;
      }
    }

    throw error;
  }
};

export { admin };

name: Main Push
on:
  push:
    branches:
      - main
jobs:
  api:
    name: API
    needs: [package-checks, repository-checks]
    runs-on: ubuntu-latest
    environment: Development
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        uses: ./.github/actions/setup
        with:
          package: "@rackup/api"
      - name: Deploy
        uses: cloudflare/wrangler-action@v3
        env:
          AUTHENTICATION_URL: ${{ vars.AUTHENTICATION_URL }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          DETECT_UNUSED_QUERY_ARGUMENTS: ${{ vars.DETECT_UNUSED_QUERY_ARGUMENTS }}
          NODE_ENV: ${{ vars.NODE_ENV }}
        with:
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          environment: development
          secrets: |
            DATABASE_URL
          vars: |
            AUTHENTICATION_URL
            DETECT_UNUSED_QUERY_ARGUMENTS
            NODE_ENV
          workingDirectory: packages/api
      - name: Migrate
        env:
          DATABASE_DIRECT_URL: ${{ secrets.DATABASE_DIRECT_URL }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: pnpm --filter @rackup/api run database:deploy
      - name: Seed
        env:
          DATABASE_DIRECT_URL: ${{ secrets.DATABASE_DIRECT_URL }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: pnpm --filter @rackup/api run database:seed
  auth:
    name: Auth
    needs: api
    runs-on: ubuntu-latest
    environment: Development
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        uses: ./.github/actions/setup
        with:
          package: "@rackup/auth"
      - name: Deploy
        uses: cloudflare/wrangler-action@v3
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          NODE_ENV: ${{ vars.NODE_ENV }}
        with:
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          environment: development
          secrets: |
            DATABASE_URL
          vars: |
            NODE_ENV
          workingDirectory: packages/auth
      - name: Seed
        env:
          ADMIN_PASSWORD: ${{ secrets.ADMIN_PASSWORD }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: pnpm --filter @rackup/auth run database:seed
  package-checks:
    name: Checks
    runs-on: ubuntu-latest
    strategy:
      matrix:
        packages: ["@rackup/api", "@rackup/auth"]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        uses: ./.github/actions/setup
        with:
          package: ${{ matrix.packages }}
      - name: Checks
        uses: ./.github/actions/package-checks
        with:
          package: ${{ matrix.packages }}
  repository-checks:
    name: Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        uses: ./.github/actions/setup
        with:
          package: "."
      - name: Checks
        uses: ./.github/actions/repository-checks

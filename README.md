# :muscle: RackUp

<!-- markdownlint-disable link-image-style -->

- [Prerequisites](#prerequisites)
- [Documentation](#documentation)
- [Authoring](#authoring)

<!-- markdownlint-enable link-image-style -->

## Prerequisites

- [Docker ↗][docker]
- [Node.js ↗][node-js] (via [Fast Node Manager ↗][fast-node-manager])
- [pnpm ↗][pnpm]

## Documentation

- [Architecture ↗][architecture]
- [DevOps ↗][devops]

## Authoring

- [Documentation ↗][documentation]

[architecture]: documentation/Architecture/README.md
[devops]: documentation/DevOps/README.md
[docker]: https://www.docker.com
[documentation]: documentation/README.md
[fast-node-manager]: https://github.com/Schniz/fnm
[node-js]: https://nodejs.org
[pnpm]: https://pnpm.io
